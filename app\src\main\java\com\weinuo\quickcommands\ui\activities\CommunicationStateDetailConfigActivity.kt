package com.weinuo.quickcommands.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueBackButton
import com.weinuo.quickcommands.ui.configuration.CommunicationStateConfigProvider
import com.weinuo.quickcommands.ui.components.IOSStyleConfigCard
import com.weinuo.quickcommands.ui.components.IOSStyleConfigCardData
import com.weinuo.quickcommands.ui.components.themed.ThemedCard
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.Icon
import androidx.compose.ui.unit.size
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.graphics.Color
import com.weinuo.quickcommands.ui.components.getUnifiedUISpacingConfig
import com.weinuo.quickcommands.ui.components.EnhancedConfigListCard
import com.weinuo.quickcommands.ui.components.EnhancedConfigItem
import androidx.compose.foundation.clickable
import androidx.compose.ui.Alignment
import com.weinuo.quickcommands.ui.theme.config.DividerConfig
import com.weinuo.quickcommands.data.SettingsRepository
import com.weinuo.quickcommands.ui.theme.skyblue.SkyBlueStyleConfiguration
import com.weinuo.quickcommands.ui.theme.manager.LocalThemeContext
import com.weinuo.quickcommands.ui.theme.manager.UISpacingConfigurationManager

import com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity
import com.weinuo.quickcommands.ui.activities.FilterModeSelectionActivity
import com.weinuo.quickcommands.ui.activities.ContactSelectionActivity
import com.weinuo.quickcommands.model.CommunicationStateType

/**
 * 通信状态详细配置Activity
 *
 * 专门用于显示通信状态配置项的详细配置界面，
 * 配置项以iOS快捷指令风格的卡片形式呈现。
 */
class CommunicationStateDetailConfigActivity : ComponentActivity() {

    companion object {
        private const val EXTRA_COMMUNICATION_TYPE = "communication_type"
        private const val EXTRA_INITIAL_CONFIG = "initial_config"

        /**
         * 启动通信状态详细配置Activity（新建模式）
         */
        fun startForCreate(context: Context, communicationType: CommunicationStateType) {
            val intent = Intent(context, CommunicationStateDetailConfigActivity::class.java).apply {
                putExtra(EXTRA_COMMUNICATION_TYPE, communicationType.name)
            }
            context.startActivity(intent)
        }

        /**
         * 启动通信状态详细配置Activity（编辑模式）
         */
        fun startForEdit(context: Context, communicationType: CommunicationStateType, initialConfig: String) {
            val intent = Intent(context, CommunicationStateDetailConfigActivity::class.java).apply {
                putExtra(EXTRA_COMMUNICATION_TYPE, communicationType.name)
                putExtra(EXTRA_INITIAL_CONFIG, initialConfig)
            }
            context.startActivity(intent)
        }
    }

    // Activity结果启动器
    private val contactRangeSelectionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val selectedRange = result.data?.getStringExtra(ContactRangeSelectionActivity.RESULT_SELECTION)
            if (selectedRange != null) {
                // 更新选择的联系人范围
                updateContactRange(selectedRange)
            }
        }
    }

    private val filterModeSelectionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val selectedMode = result.data?.getStringExtra(FilterModeSelectionActivity.RESULT_SELECTION)
            if (selectedMode != null) {
                // 更新选择的筛选模式
                updateFilterMode(selectedMode)
            }
        }
    }

    private val contactSelectionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(ContactSelectionActivity.RESULT_SELECTED_CONTACTS)
            if (resultKey != null) {
                // 更新选择的联系人
                updateSelectedContacts(resultKey)
            }
        }
    }

    private val contactGroupSelectionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val resultKey = result.data?.getStringExtra(ContactGroupSelectionActivity.RESULT_SELECTED_GROUP)
            if (resultKey != null) {
                // 更新选择的联系人分组
                updateSelectedContactGroups(resultKey)
            }
        }
    }

    // 状态变量
    private var selectedContactRange = mutableStateOf("任何联系人")
    private var selectedFilterMode = mutableStateOf("包含")
    private var selectedContacts = mutableStateOf<List<String>>(emptyList())
    private var selectedContactGroups = mutableStateOf<List<String>>(emptyList())

    private fun updateContactRange(range: String) {
        selectedContactRange.value = range
    }

    private fun updateFilterMode(mode: String) {
        selectedFilterMode.value = mode
    }

    private fun updateSelectedContacts(resultKey: String) {
        // 从ContactSelectionStorageManager读取联系人数据
        val contacts = com.weinuo.quickcommands.storage.ContactSelectionStorageManager.getSelectedContacts(resultKey)
        selectedContacts.value = contacts.map { it.name }
        // 清理临时存储
        com.weinuo.quickcommands.storage.ContactSelectionStorageManager.clearSelectedContacts(resultKey)
    }

    private fun updateSelectedContactGroups(resultKey: String) {
        // 从ContactSelectionStorageManager读取联系人分组数据
        val group = com.weinuo.quickcommands.storage.ContactSelectionStorageManager.getSelectedContactGroup(resultKey)
        if (group != null) {
            selectedContactGroups.value = listOf(group.title)
        }
        // 清理临时存储
        com.weinuo.quickcommands.storage.ContactSelectionStorageManager.clearSelectedContactGroup(resultKey)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // 获取传递的参数
        val communicationTypeString = intent.getStringExtra(EXTRA_COMMUNICATION_TYPE) ?: ""
        val initialConfigKey = intent.getStringExtra(EXTRA_INITIAL_CONFIG)

        // 解析通信状态类型
        val communicationType = try {
            CommunicationStateType.valueOf(communicationTypeString)
        } catch (e: Exception) {
            CommunicationStateType.SMS_RECEIVED // 默认值
        }

        // 加载初始配置（编辑模式）
        val initialConfig = if (initialConfigKey != null) {
            try {
                // 这里可以根据需要从存储中加载初始配置
                // 暂时返回null，表示新建模式
                null
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }

        setContent {
            QuickCommandsTheme {
                CommunicationStateDetailConfigScreen(
                    communicationType = communicationType,
                    initialConfig = initialConfig,
                    selectedContactRange = selectedContactRange.value,
                    selectedFilterMode = selectedFilterMode.value,
                    selectedContacts = selectedContacts.value,
                    selectedContactGroups = selectedContactGroups.value,
                    onContactRangeClick = { currentRange ->
                        // 启动联系人范围选择Activity
                        val intent = Intent(this@CommunicationStateDetailConfigActivity, ContactRangeSelectionActivity::class.java).apply {
                            putExtra(ContactRangeSelectionActivity.EXTRA_CURRENT_SELECTION, currentRange)
                        }
                        contactRangeSelectionLauncher.launch(intent)
                    },
                    onFilterModeClick = { currentMode ->
                        // 启动筛选模式选择Activity
                        val intent = Intent(this@CommunicationStateDetailConfigActivity, FilterModeSelectionActivity::class.java).apply {
                            putExtra(FilterModeSelectionActivity.EXTRA_CURRENT_SELECTION, currentMode)
                        }
                        filterModeSelectionLauncher.launch(intent)
                    },
                    onContactSelectionClick = {
                        // 启动联系人选择Activity
                        val intent = Intent(this@CommunicationStateDetailConfigActivity, ContactSelectionActivity::class.java).apply {
                            putExtra("selection_mode", "MULTI")
                            putExtra("result_key", "communication_contacts")
                        }
                        contactSelectionLauncher.launch(intent)
                    },
                    onContactGroupSelectionClick = {
                        // 启动联系人分组选择Activity
                        val intent = Intent(this@CommunicationStateDetailConfigActivity, ContactGroupSelectionActivity::class.java).apply {
                            putExtra("selection_mode", "SINGLE")
                            putExtra("result_key", "communication_contact_groups")
                        }
                        contactGroupSelectionLauncher.launch(intent)
                    },
                    onConfigured = { configResult ->
                        // 配置完成，关闭Activity
                        finish()
                    },
                    onNavigateBack = {
                        // 返回，关闭Activity
                        finish()
                    }
                )
            }
        }
    }
}

/**
 * 通信状态详细配置界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommunicationStateDetailConfigScreen(
    communicationType: CommunicationStateType,
    initialConfig: Any? = null,
    selectedContactRange: String,
    selectedFilterMode: String,
    selectedContacts: List<String>,
    selectedContactGroups: List<String>,
    onContactRangeClick: (String) -> Unit,
    onFilterModeClick: (String) -> Unit,
    onContactSelectionClick: () -> Unit,
    onContactGroupSelectionClick: () -> Unit,
    onConfigured: (Any) -> Unit,
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    val themeContext = LocalThemeContext.current

    // 获取动态卡片样式配置（仅在天空蓝主题下使用）
    val cardStyle = if (themeManager.getCurrentThemeId() == "sky_blue") {
        val settingsRepository = remember { SettingsRepository(context) }
        SkyBlueStyleConfiguration.getDynamicCardStyleConfig(settingsRepository)
    } else {
        themeContext.styleConfiguration.cardStyle
    }



    // 其他状态管理
    var selectedPhoneNumbers by remember { mutableStateOf<List<String>>(emptyList()) }

    // 获取配置项
    val configurationItems = remember {
        CommunicationStateConfigProvider.getConfigurationItems(context)
    }

    // 找到对应的配置项
    val targetConfigItem = remember(communicationType) {
        configurationItems.find { it.operationType == communicationType }
    }

    // 界面标题
    val screenTitle = remember(communicationType) {
        when (communicationType) {
            CommunicationStateType.SMS_RECEIVED -> "收到短信配置"
            CommunicationStateType.SMS_SENT -> "发送短信配置"
            CommunicationStateType.INCOMING_CALL -> "接到呼叫配置"
            CommunicationStateType.OUTGOING_CALL -> "拨出电话配置"
            CommunicationStateType.CALL_ACTIVE -> "通话中配置"
            CommunicationStateType.CALL_ENDED -> "呼叫结束配置"
            CommunicationStateType.MISSED_CALL -> "未接来电配置"
            CommunicationStateType.DIAL_NUMBER -> "拨打电话号码配置"
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = screenTitle,
                        style = MaterialTheme.typography.titleLarge
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        // 天空蓝主题：使用专用的返回按钮
                        SkyBlueBackButton(onClick = onNavigateBack)
                    } else {
                        // 其他主题：使用原有的箭头图标
                        IconButton(onClick = onNavigateBack) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    }
                },
                actions = {
                    // 保存按钮
                    TextButton(
                        onClick = {
                            // TODO: 收集配置并调用onConfigured
                            onConfigured(Unit)
                        }
                    ) {
                        Text("保存")
                    }
                }
            )
        }
    ) { paddingValues ->
        if (targetConfigItem != null) {
            // 使用iOS风格的配置卡片显示配置项
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 创建增强的配置项列表
                val configItems = mutableListOf<EnhancedConfigItem>().apply {
                    // 联系人范围（始终显示）
                    add(EnhancedConfigItem(
                        title = "联系人范围",
                        description = selectedContactRange,
                        isVisible = true,
                        showArrow = true,
                        onClick = {
                            // 导航到联系人范围选择页面
                            onContactRangeClick(selectedContactRange)
                        }
                    ))

                    // 根据选择的联系人范围动态添加其他选项
                    when (selectedContactRange) {
                        "指定联系人" -> {
                            add(EnhancedConfigItem(
                                title = "联系人",
                                description = if (selectedContacts.isEmpty()) "选取" else "${selectedContacts.size}个联系人",
                                isVisible = true,
                                showArrow = true,
                                onClick = {
                                    // 打开联系人选择器
                                    onContactSelectionClick()
                                }
                            ))
                            add(EnhancedConfigItem(
                                title = "筛选模式",
                                description = selectedFilterMode,
                                isVisible = true,
                                showArrow = true,
                                onClick = {
                                    // 打开筛选模式选择页面
                                    onFilterModeClick(selectedFilterMode)
                                }
                            ))
                        }
                        "指定分组" -> {
                            add(EnhancedConfigItem(
                                title = "联系人组",
                                description = if (selectedContactGroups.isEmpty()) "选取" else "${selectedContactGroups.size}个分组",
                                isVisible = true,
                                showArrow = true,
                                onClick = {
                                    // 打开联系人组选择器
                                    onContactGroupSelectionClick()
                                }
                            ))
                        }
                        "指定号码" -> {
                            add(EnhancedConfigItem(
                                title = "电话号码",
                                description = if (selectedPhoneNumbers.isEmpty()) "添加" else "${selectedPhoneNumbers.size}个号码",
                                isVisible = true,
                                showArrow = true,
                                onClick = {
                                    // 打开号码输入页面（暂时显示Toast提示）
                                    android.widget.Toast.makeText(context, "号码输入功能开发中", android.widget.Toast.LENGTH_SHORT).show()
                                }
                            ))
                        }
                    }
                }

                item {
                    // 使用增强的配置列表卡片组件
                    EnhancedConfigListCard(
                        configItems = configItems,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
        } else {
            // 配置项不存在的错误处理
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
            ) {
                Text(
                    text = "配置项不存在",
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(16.dp)
                )
            }
        }
    }
}
