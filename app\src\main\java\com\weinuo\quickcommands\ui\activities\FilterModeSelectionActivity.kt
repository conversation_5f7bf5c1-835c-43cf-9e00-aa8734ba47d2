package com.weinuo.quickcommands.ui.activities

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueBackButton
import com.weinuo.quickcommands.ui.components.UnifiedSelectionListCard

/**
 * 筛选模式选择Activity
 */
class FilterModeSelectionActivity : ComponentActivity() {

    companion object {
        const val EXTRA_CURRENT_SELECTION = "current_selection"
        const val RESULT_SELECTION = "selection_result"

        fun startForResult(context: Context, currentSelection: String = "包含") {
            val intent = Intent(context, FilterModeSelectionActivity::class.java).apply {
                putExtra(EXTRA_CURRENT_SELECTION, currentSelection)
            }
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        val currentSelection = intent.getStringExtra(EXTRA_CURRENT_SELECTION) ?: "包含"

        setContent {
            QuickCommandsTheme {
                FilterModeSelectionScreen(
                    currentSelection = currentSelection,
                    onSelectionChanged = { selection ->
                        // 返回选择结果
                        val resultIntent = Intent().apply {
                            putExtra(RESULT_SELECTION, selection)
                        }
                        setResult(RESULT_OK, resultIntent)
                        finish()
                    },
                    onNavigateBack = {
                        finish()
                    }
                )
            }
        }
    }
}

/**
 * 筛选模式选择界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FilterModeSelectionScreen(
    currentSelection: String,
    onSelectionChanged: (String) -> Unit,
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }

    // 筛选模式选项
    val filterModeOptions = listOf(
        "包含",
        "排除"
    )

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "筛选模式",
                        style = MaterialTheme.typography.titleLarge
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        SkyBlueBackButton(onClick = onNavigateBack)
                    } else {
                        IconButton(onClick = onNavigateBack) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                UnifiedSelectionListCard(
                    options = filterModeOptions,
                    currentSelection = currentSelection,
                    onSelectionChanged = onSelectionChanged
                )
            }
        }
    }
}


