# -*- coding: utf-8 -*-
# final_review_gate.py
import sys

if __name__ == "__main__":
    print("""解决问题：2025-08-05 22:15:39.931 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  the up motion event handled by client, just return
2025-08-05 22:15:39.942 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 52.3487 refreshRate 16686882 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:39.946 17574-17574 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=null, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:15:39.987 17574-17574 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:15:40.072 17574-17574 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:15:40.073 17574-17574 ActivityThread          com.weinuo.quickcommands             D  ComponentInfo{com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity} checkFinished=false 2
2025-08-05 22:15:40.073 17574-17574 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:15:40.079 17574-17574 WindowManager           com.weinuo.quickcommands             D  Add to mViews: DecorView@aacc19b[CommunicationStateDetailConfigActivity],pkg= com.weinuo.quickcommands
2025-08-05 22:15:40.091 17574-17574 InputEventReceiver      com.weinuo.quickcommands             D  Input log is disabled in InputEventReceiver.
2025-08-05 22:15:40.092 17574-17574 VRI[Detail...nActivity] com.weinuo.quickcommands             D  onFocusEvent false
2025-08-05 22:15:40.093 17574-17574 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:15:40.094 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 1 cost 21.796513 refreshRate 16683913 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:40.095 17574-17574 OplusBracketLog         com.weinuo.quickcommands             E  [OplusViewMirrorManager] updateHostViewRootIfNeeded, not support android.view.ViewRootImpl@a05de76
2025-08-05 22:15:40.115 17574-17574 Vibrator                com.weinuo.quickcommands             D  SystemVibrator Created
2025-08-05 22:15:40.121 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:40.127 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:40.153 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:40.158 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:40.210 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:40.212 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:40.218 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:40.250 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity
2025-08-05 22:15:40.271 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:true, surfaceControlChanged:true, transformHintChanged:true, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(0, 0), mWidth:-1, mHeight:-1, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:-2147483648, installOrientation:0, displayRotation:0, isSurfaceValid:true, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:15:40.272 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             W  updateBlastSurfaceIfNeeded, surfaceSize:Point(1080, 2400), lastSurfaceSize:Point(0, 0), format:-1, blastBufferQueue:null
2025-08-05 22:15:40.272 17574-17574 BufferQueueConsumer     com.weinuo.quickcommands             D  [](id:44a600000004,api:0,p:-1,c:17574) connect: controlledByApp=false
2025-08-05 22:15:40.276 17574-18831 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#4(BLAST Consumer)4](id:44a600000004,api:1,p:17574,c:17574) connect: api=1 producerControlledByApp=true
2025-08-05 22:15:40.277 17574-18831 OpenGLRenderer          com.weinuo.quickcommands             E  Unable to match the desired swap behavior.
2025-08-05 22:15:40.292 17574-17574 SurfaceControl          com.weinuo.quickcommands             I   setExtendedRangeBrightness sc=Surface(name=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity)/@0x7cf0778,currentBufferRatio=1.0,desiredRatio=1.0
2025-08-05 22:15:40.312 17574-18831 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#4](f:0,a:1) acquireNextBufferLocked size=1080x2400 mFrameNumber=1 applyTransaction=true mTimestamp=76266921108978(auto) mPendingTransactions.size=0 graphicBufferId=75479755259923 transform=0
2025-08-05 22:15:40.313 17574-18831 VRI[Commun...gActivity] com.weinuo.quickcommands             D  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true syncBuffer=false
2025-08-05 22:15:40.314 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  draw finished.
2025-08-05 22:15:40.314 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  reportDrawFinished
2025-08-05 22:15:40.314 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  setMaxDequeuedBufferCount: 2
2025-08-05 22:15:40.318 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  === 当前主题调试信息 ===
2025-08-05 22:15:40.319 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  主题ID: sky_blue
2025-08-05 22:15:40.319 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  主题名称: 天空蓝
2025-08-05 22:15:40.319 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  设计方法: INTEGRATED_DESIGN
2025-08-05 22:15:40.319 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  支持模糊: true
2025-08-05 22:15:40.319 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  支持阴影: false
2025-08-05 22:15:40.319 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  支持透明: true
2025-08-05 22:15:40.319 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  ========================
2025-08-05 22:15:40.320 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  === 颜色配置读取 ===
2025-08-05 22:15:40.320 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  Primary: 0xFF0A59F7
2025-08-05 22:15:40.320 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  Background: 0xFFF1F3F5
2025-08-05 22:15:40.320 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  Surface: 0xFFF1F3F5
2025-08-05 22:15:40.320 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  ==================
2025-08-05 22:15:40.320 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  === 天空蓝动态颜色配置 ===
2025-08-05 22:15:40.320 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  Primary: Color(0.039215688, 0.34901962, 0.96862745, 1.0, sRGB IEC61966-2.1)
2025-08-05 22:15:40.320 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  Background: Color(0.94509804, 0.9529412, 0.9607843, 1.0, sRGB IEC61966-2.1)
2025-08-05 22:15:40.321 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  Surface: Color(0.94509804, 0.9529412, 0.9607843, 1.0, sRGB IEC61966-2.1)
2025-08-05 22:15:40.321 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  ========================
2025-08-05 22:15:40.325 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 13 cost 219.96109 refreshRate 16683989 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:40.328 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             W  handleResized, msg:5, frameChanged:false, configChanged:false, displayChanged:false, attachedFrameChanged:false, compatScaleChanged:false, pendingDragResizing=false
2025-08-05 22:15:40.342 17574-18831 VRI[Commun...gActivity] com.weinuo.quickcommands             D  Received frameCommittedCallback lastAttemptedDrawFrameNum=2 didProduceBuffer=false syncBuffer=false
2025-08-05 22:15:40.342 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  draw finished.
2025-08-05 22:15:40.342 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  reportDrawFinished
2025-08-05 22:15:40.479 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  onFocusEvent true
2025-08-05 22:15:40.859 17574-18859 VRI[Detail...nActivity] com.weinuo.quickcommands             D  dispatchAppVisibility visible:false
2025-08-05 22:15:40.874 17574-17574 OpenGLRenderer          com.weinuo.quickcommands             D  RenderProxy::destroy: this=0xb4000078a12e3780, mContext=0xb40000788ec4e3c0
2025-08-05 22:15:40.874 17574-18831 OpenGLRenderer          com.weinuo.quickcommands             D  SkiaOpenGLPipeline::setSurface: this=0xb40000788ea513c0, surface=NULL
2025-08-05 22:15:40.875 17574-18831 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[DetailedConfigurationActivity]#3(BLAST Consumer)3](id:44a600000003,api:1,p:17574,c:17574) disconnect: api 1
2025-08-05 22:15:40.875 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.DetailedConfigurationActivity
2025-08-05 22:15:40.884 17574-17574 VRI[Detail...nActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:false, surfaceControlChanged:true, transformHintChanged:false, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(1080, 2400), mWidth:1080, mHeight:2400, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:0, installOrientation:0, displayRotation:0, isSurfaceValid:false, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:15:40.885 17574-17574 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[DetailedConfigurationActivity]#3](f:0,a:2) destructor()
2025-08-05 22:15:40.885 17574-17574 BufferQueueConsumer     com.weinuo.quickcommands             D  [VRI[DetailedConfigurationActivity]#3(BLAST Consumer)3](id:44a600000003,api:0,p:-1,c:17574) disconnect
2025-08-05 22:15:40.887 17574-17574 VRI[Detail...nActivity] com.weinuo.quickcommands             D  setWindowStopped stopped:true
2025-08-05 22:15:41.101 17574-17574 OplusSyste...ureExtImpl com.weinuo.quickcommands             D  get regionString is null Attempt to invoke virtual method 'android.content.ContentResolver android.content.Context.getContentResolver()' on a null object reference
2025-08-05 22:15:41.101 17574-17574 OplusSyste...ureExtImpl com.weinuo.quickcommands             D  regionString = null
2025-08-05 22:15:41.201 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  the up motion event handled by client, just return
2025-08-05 22:15:41.212 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 1 cost 21.593445 refreshRate 16683755 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:41.216 17574-17574 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=null, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:15:41.259 17574-17574 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:15:41.318 17574-18857 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.DetailedConfigurationActivity,This DecorView@552afb[DetailedConfigurationActivity] change focus to false
2025-08-05 22:15:41.343 17574-17574 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:15:41.344 17574-17574 ActivityThread          com.weinuo.quickcommands             D  ComponentInfo{com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity} checkFinished=false 2
2025-08-05 22:15:41.345 17574-17574 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:15:41.360 17574-17574 WindowManager           com.weinuo.quickcommands             D  Add to mViews: DecorView@aa8d3f0[ContactRangeSelectionActivity],pkg= com.weinuo.quickcommands
2025-08-05 22:15:41.379 17574-17574 InputEventReceiver      com.weinuo.quickcommands             D  Input log is disabled in InputEventReceiver.
2025-08-05 22:15:41.380 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  onFocusEvent false
2025-08-05 22:15:41.381 17574-17574 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:15:41.383 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 2 cost 40.006306 refreshRate 16684080 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:41.384 17574-17574 OplusBracketLog         com.weinuo.quickcommands             E  [OplusViewMirrorManager] updateHostViewRootIfNeeded, not support android.view.ViewRootImpl@836408f
2025-08-05 22:15:41.403 17574-17574 Vibrator                com.weinuo.quickcommands             D  SystemVibrator Created
2025-08-05 22:15:41.407 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:41.436 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:41.441 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:41.479 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:41.480 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:41.482 17574-18857 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity,This DecorView@aacc19b[CommunicationStateDetailConfigActivity] change focus to true
2025-08-05 22:15:41.484 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:41.536 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity
2025-08-05 22:15:41.551 17574-17574 VRI[Contac...nActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:true, surfaceControlChanged:true, transformHintChanged:true, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(0, 0), mWidth:-1, mHeight:-1, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:-2147483648, installOrientation:0, displayRotation:0, isSurfaceValid:true, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:15:41.552 17574-17574 VRI[Contac...nActivity] com.weinuo.quickcommands             W  updateBlastSurfaceIfNeeded, surfaceSize:Point(1080, 2400), lastSurfaceSize:Point(0, 0), format:-1, blastBufferQueue:null
2025-08-05 22:15:41.552 17574-17574 BufferQueueConsumer     com.weinuo.quickcommands             D  [](id:44a600000005,api:0,p:-1,c:17574) connect: controlledByApp=false
2025-08-05 22:15:41.557 17574-18831 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[ContactRangeSelectionActivity]#5(BLAST Consumer)5](id:44a600000005,api:1,p:17574,c:17574) connect: api=1 producerControlledByApp=true
2025-08-05 22:15:41.557 17574-18831 OpenGLRenderer          com.weinuo.quickcommands             E  Unable to match the desired swap behavior.
2025-08-05 22:15:41.574 17574-17574 SurfaceControl          com.weinuo.quickcommands             I   setExtendedRangeBrightness sc=Surface(name=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity)/@0x1ff48d7,currentBufferRatio=1.0,desiredRatio=1.0
2025-08-05 22:15:41.597 17574-18831 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[ContactRangeSelectionActivity]#5](f:0,a:1) acquireNextBufferLocked size=1080x2400 mFrameNumber=1 applyTransaction=true mTimestamp=76268205948560(auto) mPendingTransactions.size=0 graphicBufferId=75479755259927 transform=0
2025-08-05 22:15:41.598 17574-18831 VRI[Contac...nActivity] com.weinuo.quickcommands             D  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true syncBuffer=false
2025-08-05 22:15:41.599 17574-17574 VRI[Contac...nActivity] com.weinuo.quickcommands             D  draw finished.
2025-08-05 22:15:41.599 17574-17574 VRI[Contac...nActivity] com.weinuo.quickcommands             D  reportDrawFinished
2025-08-05 22:15:41.600 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  setMaxDequeuedBufferCount: 2
2025-08-05 22:15:41.604 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  === 当前主题调试信息 ===
2025-08-05 22:15:41.604 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  主题ID: sky_blue
2025-08-05 22:15:41.604 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  主题名称: 天空蓝
2025-08-05 22:15:41.604 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  设计方法: INTEGRATED_DESIGN
2025-08-05 22:15:41.604 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  支持模糊: true
2025-08-05 22:15:41.604 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  支持阴影: false
2025-08-05 22:15:41.604 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  支持透明: true
2025-08-05 22:15:41.604 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  ========================
2025-08-05 22:15:41.604 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  === 颜色配置读取 ===
2025-08-05 22:15:41.605 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  Primary: 0xFF0A59F7
2025-08-05 22:15:41.605 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  Background: 0xFFF1F3F5
2025-08-05 22:15:41.605 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  Surface: 0xFFF1F3F5
2025-08-05 22:15:41.605 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  ==================
2025-08-05 22:15:41.605 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  === 天空蓝动态颜色配置 ===
2025-08-05 22:15:41.605 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  Primary: Color(0.039215688, 0.34901962, 0.96862745, 1.0, sRGB IEC61966-2.1)
2025-08-05 22:15:41.605 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  Background: Color(0.94509804, 0.9529412, 0.9607843, 1.0, sRGB IEC61966-2.1)
2025-08-05 22:15:41.605 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  Surface: Color(0.94509804, 0.9529412, 0.9607843, 1.0, sRGB IEC61966-2.1)
2025-08-05 22:15:41.605 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  ========================
2025-08-05 22:15:41.609 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 13 cost 219.39026 refreshRate 16683514 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:41.612 17574-17574 VRI[Contac...nActivity] com.weinuo.quickcommands             W  handleResized, msg:5, frameChanged:false, configChanged:false, displayChanged:false, attachedFrameChanged:false, compatScaleChanged:false, pendingDragResizing=false
2025-08-05 22:15:41.627 17574-18831 VRI[Contac...nActivity] com.weinuo.quickcommands             D  Received frameCommittedCallback lastAttemptedDrawFrameNum=2 didProduceBuffer=false syncBuffer=false
2025-08-05 22:15:41.628 17574-17574 VRI[Contac...nActivity] com.weinuo.quickcommands             D  draw finished.
2025-08-05 22:15:41.628 17574-17574 VRI[Contac...nActivity] com.weinuo.quickcommands             D  reportDrawFinished
2025-08-05 22:15:41.763 17574-17574 VRI[Contac...nActivity] com.weinuo.quickcommands             D  onFocusEvent true
2025-08-05 22:15:42.140 17574-18859 VRI[Commun...gActivity] com.weinuo.quickcommands             D  dispatchAppVisibility visible:false
2025-08-05 22:15:42.159 17574-17574 OpenGLRenderer          com.weinuo.quickcommands             D  RenderProxy::destroy: this=0xb40000790a570b00, mContext=0xb40000788ecb9100
2025-08-05 22:15:42.159 17574-18831 OpenGLRenderer          com.weinuo.quickcommands             D  SkiaOpenGLPipeline::setSurface: this=0xb40000792d515d00, surface=NULL
2025-08-05 22:15:42.160 17574-18831 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#4(BLAST Consumer)4](id:44a600000004,api:1,p:17574,c:17574) disconnect: api 1
2025-08-05 22:15:42.161 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity
2025-08-05 22:15:42.170 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:false, surfaceControlChanged:true, transformHintChanged:false, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(1080, 2400), mWidth:1080, mHeight:2400, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:0, installOrientation:0, displayRotation:0, isSurfaceValid:false, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:15:42.171 17574-17574 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#4](f:0,a:1) destructor()
2025-08-05 22:15:42.171 17574-17574 BufferQueueConsumer     com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#4(BLAST Consumer)4](id:44a600000004,api:0,p:-1,c:17574) disconnect
2025-08-05 22:15:42.173 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  setWindowStopped stopped:true
2025-08-05 22:15:42.605 17574-18857 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity,This DecorView@aacc19b[CommunicationStateDetailConfigActivity] change focus to false
2025-08-05 22:15:42.628 17574-17574 OplusSyste...ureExtImpl com.weinuo.quickcommands             D  get regionString is null Attempt to invoke virtual method 'android.content.ContentResolver android.content.Context.getContentResolver()' on a null object reference
2025-08-05 22:15:42.628 17574-17574 OplusSyste...ureExtImpl com.weinuo.quickcommands             D  regionString = null
2025-08-05 22:15:42.700 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  the up motion event handled by client, just return
2025-08-05 22:15:42.710 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 1 cost 18.745472 refreshRate 16682558 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:42.714 17574-17574 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=null, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:15:42.720 17574-18859 VRI[Commun...gActivity] com.weinuo.quickcommands             D  dispatchAppVisibility visible:true
2025-08-05 22:15:42.738 17574-17574 VRI[Contac...nActivity] com.weinuo.quickcommands             D  onFocusEvent false
2025-08-05 22:15:42.745 17574-17574 Compatibil...geReporter com.weinuo.quickcommands             D  Compat change id reported: 78294732; UID 11011; state: ENABLED
2025-08-05 22:15:42.747 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  setWindowStopped stopped:false
2025-08-05 22:15:42.755 17574-17574 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:15:42.755 17574-17574 ActivityThread          com.weinuo.quickcommands             D  ComponentInfo{com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity} checkFinished=false 2
2025-08-05 22:15:42.756 17574-17574 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:15:42.782 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 1 cost 23.485292 refreshRate 16683061 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:42.931 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity
2025-08-05 22:15:42.960 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:false, surfaceControlChanged:true, transformHintChanged:false, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(1080, 2400), mWidth:1080, mHeight:2400, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:0, installOrientation:0, displayRotation:0, isSurfaceValid:true, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:15:42.961 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             W  updateBlastSurfaceIfNeeded, surfaceSize:Point(1080, 2400), lastSurfaceSize:Point(1080, 2400), format:-1, blastBufferQueue:null
2025-08-05 22:15:42.961 17574-17574 BufferQueueConsumer     com.weinuo.quickcommands             D  [](id:44a600000006,api:0,p:-1,c:17574) connect: controlledByApp=false
2025-08-05 22:15:42.965 17574-18831 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#6(BLAST Consumer)6](id:44a600000006,api:1,p:17574,c:17574) connect: api=1 producerControlledByApp=true
2025-08-05 22:15:42.965 17574-18831 OpenGLRenderer          com.weinuo.quickcommands             E  Unable to match the desired swap behavior.
2025-08-05 22:15:42.996 17574-17574 SurfaceControl          com.weinuo.quickcommands             I   setExtendedRangeBrightness sc=Surface(name=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity)/@0x7cf0778,currentBufferRatio=1.0,desiredRatio=1.0
2025-08-05 22:15:43.053 17574-18831 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#6](f:0,a:1) acquireNextBufferLocked size=1080x2400 mFrameNumber=1 applyTransaction=true mTimestamp=76269661487779(auto) mPendingTransactions.size=0 graphicBufferId=75479755259931 transform=0
2025-08-05 22:15:43.053 17574-18831 VRI[Commun...gActivity] com.weinuo.quickcommands             D  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true syncBuffer=false
2025-08-05 22:15:43.054 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  draw finished.
2025-08-05 22:15:43.054 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  reportDrawFinished
2025-08-05 22:15:43.056 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  setMaxDequeuedBufferCount: 2
2025-08-05 22:15:43.071 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 15 cost 262.24332 refreshRate 16683061 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:43.114 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  onFocusEvent true
2025-08-05 22:15:43.154 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             W  handleResized abandoned!
2025-08-05 22:15:43.618 17574-17606 VRI[Contac...nActivity] com.weinuo.quickcommands             D  dispatchAppVisibility visible:false
2025-08-05 22:15:43.632 17574-17574 OpenGLRenderer          com.weinuo.quickcommands             D  RenderProxy::destroy: this=0xb40000788ebc1680, mContext=0xb40000788ed2b9c0
2025-08-05 22:15:43.632 17574-18831 OpenGLRenderer          com.weinuo.quickcommands             D  SkiaOpenGLPipeline::setSurface: this=0xb40000788ea54340, surface=NULL
2025-08-05 22:15:43.633 17574-18831 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[ContactRangeSelectionActivity]#5(BLAST Consumer)5](id:44a600000005,api:1,p:17574,c:17574) disconnect: api 1
2025-08-05 22:15:43.635 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity
2025-08-05 22:15:43.658 17574-17574 VRI[Contac...nActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:false, surfaceControlChanged:true, transformHintChanged:false, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(1080, 2400), mWidth:1080, mHeight:2400, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:0, installOrientation:0, displayRotation:0, isSurfaceValid:false, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:15:43.660 17574-17574 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[ContactRangeSelectionActivity]#5](f:0,a:1) destructor()
2025-08-05 22:15:43.660 17574-17574 BufferQueueConsumer     com.weinuo.quickcommands             D  [VRI[ContactRangeSelectionActivity]#5(BLAST Consumer)5](id:44a600000005,api:0,p:-1,c:17574) disconnect
2025-08-05 22:15:43.665 17574-17574 VRI[Contac...nActivity] com.weinuo.quickcommands             D  setWindowStopped stopped:true
2025-08-05 22:15:43.744 17574-18857 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity,This DecorView@aa8d3f0[ContactRangeSelectionActivity] change focus to false
2025-08-05 22:15:43.744 17574-17574 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity,window dying
2025-08-05 22:15:43.745 17574-17574 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity,unregisterSystemUIBroadcastReceiver 
2025-08-05 22:15:43.746 17574-17574 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity, unregisterSystemUIBroadcastReceiver failed java.lang.IllegalArgumentException: Receiver not registered: android.view.OplusScrollToTopManager$2@c90eaa2
2025-08-05 22:15:43.749 17574-17574 WindowOnBackDispatcher  com.weinuo.quickcommands             W  sendCancelIfRunning: isInProgress=falsecallback=android.view.ViewRootImpl$$ExternalSyntheticLambda17@b07955f
2025-08-05 22:15:43.764 17574-17574 OpenGLRenderer          com.weinuo.quickcommands             D  RenderProxy::destroy: this=0xb40000788ebc1680, mContext=0xb40000788ed2b9c0
2025-08-05 22:15:43.764 17574-18831 OpenGLRenderer          com.weinuo.quickcommands             D  SkiaOpenGLPipeline::setSurface: this=0xb40000788ea54340, surface=NULL
2025-08-05 22:15:43.850 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  the up motion event handled by client, just return
2025-08-05 22:15:43.853 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: true 10 cost 176.61734 refreshRate 16686988 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:43.898 17574-17574 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=null, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:15:43.940 17574-17574 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:15:44.024 17574-17574 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:15:44.025 17574-17574 ActivityThread          com.weinuo.quickcommands             D  ComponentInfo{com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity} checkFinished=false 2
2025-08-05 22:15:44.026 17574-17574 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:15:44.040 17574-17574 WindowManager           com.weinuo.quickcommands             D  Add to mViews: DecorView@5b68a4c[PhoneNumberInputActivity],pkg= com.weinuo.quickcommands
2025-08-05 22:15:44.062 17574-17574 InputEventReceiver      com.weinuo.quickcommands             D  Input log is disabled in InputEventReceiver.
2025-08-05 22:15:44.063 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  onFocusEvent false
2025-08-05 22:15:44.064 17574-17574 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:15:44.066 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 2 cost 41.65474 refreshRate 16679389 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:44.068 17574-17574 OplusBracketLog         com.weinuo.quickcommands             E  [OplusViewMirrorManager] updateHostViewRootIfNeeded, not support android.view.ViewRootImpl@5bfbf9b
2025-08-05 22:15:44.086 17574-17574 Vibrator                com.weinuo.quickcommands             D  SystemVibrator Created
2025-08-05 22:15:44.091 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:44.119 17574-18857 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity,This DecorView@aacc19b[CommunicationStateDetailConfigActivity] change focus to true
2025-08-05 22:15:44.120 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:44.125 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:44.231 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:44.237 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:44.245 17574-17574 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:15:44.300 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity
2025-08-05 22:15:44.321 17574-17574 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:true, surfaceControlChanged:true, transformHintChanged:true, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(0, 0), mWidth:-1, mHeight:-1, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:-2147483648, installOrientation:0, displayRotation:0, isSurfaceValid:true, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:15:44.322 17574-17574 VRI[PhoneN...tActivity] com.weinuo.quickcommands             W  updateBlastSurfaceIfNeeded, surfaceSize:Point(1080, 2400), lastSurfaceSize:Point(0, 0), format:-1, blastBufferQueue:null
2025-08-05 22:15:44.322 17574-17574 BufferQueueConsumer     com.weinuo.quickcommands             D  [](id:44a600000007,api:0,p:-1,c:17574) connect: controlledByApp=false
2025-08-05 22:15:44.326 17574-18831 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[PhoneNumberInputActivity]#7(BLAST Consumer)7](id:44a600000007,api:1,p:17574,c:17574) connect: api=1 producerControlledByApp=true
2025-08-05 22:15:44.326 17574-18831 OpenGLRenderer          com.weinuo.quickcommands             E  Unable to match the desired swap behavior.
2025-08-05 22:15:44.345 17574-17574 SurfaceControl          com.weinuo.quickcommands             I   setExtendedRangeBrightness sc=Surface(name=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity)/@0x260e4a8,currentBufferRatio=1.0,desiredRatio=1.0
2025-08-05 22:15:44.367 17574-18831 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[PhoneNumberInputActivity]#7](f:0,a:1) acquireNextBufferLocked size=1080x2400 mFrameNumber=1 applyTransaction=true mTimestamp=76270975645538(auto) mPendingTransactions.size=0 graphicBufferId=75479755259935 transform=0
2025-08-05 22:15:44.367 17574-18831 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true syncBuffer=false
2025-08-05 22:15:44.368 17574-17574 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  draw finished.
2025-08-05 22:15:44.368 17574-17574 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  reportDrawFinished
2025-08-05 22:15:44.369 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  setMaxDequeuedBufferCount: 2
2025-08-05 22:15:44.373 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  === 当前主题调试信息 ===
2025-08-05 22:15:44.373 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  主题ID: sky_blue
2025-08-05 22:15:44.373 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  主题名称: 天空蓝
2025-08-05 22:15:44.373 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  设计方法: INTEGRATED_DESIGN
2025-08-05 22:15:44.373 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  支持模糊: true
2025-08-05 22:15:44.373 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  支持阴影: false
2025-08-05 22:15:44.373 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  支持透明: true
2025-08-05 22:15:44.373 17574-17574 AppThemeProvider        com.weinuo.quickcommands             D  ========================
2025-08-05 22:15:44.374 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  === 颜色配置读取 ===
2025-08-05 22:15:44.374 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  Primary: 0xFF0A59F7
2025-08-05 22:15:44.374 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  Background: 0xFFF1F3F5
2025-08-05 22:15:44.374 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  Surface: 0xFFF1F3F5
2025-08-05 22:15:44.374 17574-17574 SkyBlueColorConfig      com.weinuo.quickcommands             D  ==================
2025-08-05 22:15:44.374 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  === 天空蓝动态颜色配置 ===
2025-08-05 22:15:44.375 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  Primary: Color(0.039215688, 0.34901962, 0.96862745, 1.0, sRGB IEC61966-2.1)
2025-08-05 22:15:44.375 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  Background: Color(0.94509804, 0.9529412, 0.9607843, 1.0, sRGB IEC61966-2.1)
2025-08-05 22:15:44.375 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  Surface: Color(0.94509804, 0.9529412, 0.9607843, 1.0, sRGB IEC61966-2.1)
2025-08-05 22:15:44.375 17574-17574 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  ========================
2025-08-05 22:15:44.383 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 18 cost 307.15338 refreshRate 16677876 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:44.396 17574-17574 VRI[PhoneN...tActivity] com.weinuo.quickcommands             W  handleResized, msg:5, frameChanged:false, configChanged:false, displayChanged:false, attachedFrameChanged:false, compatScaleChanged:false, pendingDragResizing=false
2025-08-05 22:15:44.406 17574-18831 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  Received frameCommittedCallback lastAttemptedDrawFrameNum=3 didProduceBuffer=false syncBuffer=false
2025-08-05 22:15:44.407 17574-17574 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  draw finished.
2025-08-05 22:15:44.407 17574-17574 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  reportDrawFinished
2025-08-05 22:15:44.548 17574-17574 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  onFocusEvent true
2025-08-05 22:15:44.936 17574-17607 VRI[Commun...gActivity] com.weinuo.quickcommands             D  dispatchAppVisibility visible:false
2025-08-05 22:15:44.948 17574-17574 OpenGLRenderer          com.weinuo.quickcommands             D  RenderProxy::destroy: this=0xb40000790a570b00, mContext=0xb40000788ecb9100
2025-08-05 22:15:44.948 17574-18831 OpenGLRenderer          com.weinuo.quickcommands             D  SkiaOpenGLPipeline::setSurface: this=0xb40000792d515d00, surface=NULL
2025-08-05 22:15:44.949 17574-18831 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#6(BLAST Consumer)6](id:44a600000006,api:1,p:17574,c:17574) disconnect: api 1
2025-08-05 22:15:44.951 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity
2025-08-05 22:15:44.971 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:false, surfaceControlChanged:true, transformHintChanged:false, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(1080, 2400), mWidth:1080, mHeight:2400, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:0, installOrientation:0, displayRotation:0, isSurfaceValid:false, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:15:44.972 17574-17574 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#6](f:0,a:1) destructor()
2025-08-05 22:15:44.972 17574-17574 BufferQueueConsumer     com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#6(BLAST Consumer)6](id:44a600000006,api:0,p:-1,c:17574) disconnect
2025-08-05 22:15:44.978 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  setWindowStopped stopped:true
2025-08-05 22:15:45.296 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  the up motion event handled by client, just return
2025-08-05 22:15:45.369 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 9 cost 158.09729 refreshRate 16683827 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:45.373 17574-18857 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity,This DecorView@aacc19b[CommunicationStateDetailConfigActivity] change focus to false
2025-08-05 22:15:45.454 17574-17574 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:8b02d0b1: onRequestShow at ORIGIN_CLIENT_SHOW_SOFT_INPUT reason SHOW_SOFT_INPUT_BY_INSETS_API
2025-08-05 22:15:45.454 17574-17574 InsetsController        com.weinuo.quickcommands             D  show(ime(), fromIme=false)
2025-08-05 22:15:45.461 17574-17574 InputMethodManager      com.weinuo.quickcommands             D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{df2274d VFED..... .F....ID 0,0-1080,2400 aid=1073741824 viewInfo = } flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
2025-08-05 22:15:45.530 17574-17574 ViewExtract             com.weinuo.quickcommands             D  [OplusViewExtractManager] OplusViewExtractManager initcom.weinuo.quickcommands
2025-08-05 22:15:45.556 17574-18857 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity,This DecorView@5b68a4c[PhoneNumberInputActivity] change focus to true
2025-08-05 22:15:45.564 17574-17574 AssistStructure         com.weinuo.quickcommands             I  Flattened final assist data: 2684 bytes, containing 1 windows, 7 views
2025-08-05 22:15:45.585 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 12 cost 206.6548 refreshRate 16683827 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:45.607 17574-17574 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:ba3562c8: onRequestShow at ORIGIN_CLIENT_SHOW_SOFT_INPUT reason SHOW_SOFT_INPUT
2025-08-05 22:15:45.609 17574-17574 InputMethodManager      com.weinuo.quickcommands             D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{df2274d VFED..... .F...... 0,0-1080,2400 aid=1073741824 viewInfo = } flags=0 reason=SHOW_SOFT_INPUT
2025-08-05 22:15:45.634 17574-17574 VRI[PhoneN...tActivity] com.weinuo.quickcommands             W  handleResized abandoned!
2025-08-05 22:15:45.636 17574-17574 InsetsController        com.weinuo.quickcommands             D  show(ime(), fromIme=true)
2025-08-05 22:15:45.645 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 50.392105 refreshRate 16682936 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:45.682 17574-17574 InsetsController        com.weinuo.quickcommands             D  show(ime(), fromIme=true)
2025-08-05 22:15:45.684 17574-17574 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:ba3562c8: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-08-05 22:15:45.692 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 30.827011 refreshRate 16682364 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:45.704 17574-17574 VRI[PhoneN...tActivity] com.weinuo.quickcommands             W  handleResized abandoned!
2025-08-05 22:15:45.986 17574-17574 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:8b02d0b1: onShown
2025-08-05 22:15:46.460 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 4 cost 81.17065 refreshRate 16685718 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:46.610 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 2 cost 47.57279 refreshRate 16685098 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:46.793 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 2 cost 46.940563 refreshRate 16683979 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:47.380 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 17.201048 refreshRate 16685187 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:47.466 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 52.528553 refreshRate 16684943 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:47.569 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 21.858189 refreshRate 16684336 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:47.670 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 56.323326 refreshRate 16684164 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:47.938 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 57.75928 refreshRate 16683904 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:48.233 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 18.52394 refreshRate 16683920 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:48.333 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 52.159782 refreshRate 16683920 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:48.571 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 23.686785 refreshRate 16683440 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:48.670 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 56.035854 refreshRate 16683568 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:48.936 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 20.939924 refreshRate 16683913 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:49.036 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 54.088947 refreshRate 16684630 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:49.354 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 55.22138 refreshRate 16684603 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:49.468 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 19.731012 refreshRate 16684519 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:49.567 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 51.069565 refreshRate 16684343 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:50.117 17574-17574 PhoneNumberInput        com.weinuo.quickcommands             D  保存按钮点击，号码：13618606542
2025-08-05 22:15:50.118 17574-17574 PhoneNumberInput        com.weinuo.quickcommands             D  onNumberChanged调用，号码：13618606542
2025-08-05 22:15:50.137 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  the up motion event handled by client, just return
2025-08-05 22:15:50.165 17574-17574 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=null, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:15:50.173 17574-17606 VRI[Commun...gActivity] com.weinuo.quickcommands             D  dispatchAppVisibility visible:true
2025-08-05 22:15:50.192 17574-17574 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  onFocusEvent false
2025-08-05 22:15:50.208 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  setWindowStopped stopped:false
2025-08-05 22:15:50.214 17574-17574 CommunicationConfig     com.weinuo.quickcommands             D  phoneNumberInputLauncher结果：resultCode=-1
2025-08-05 22:15:50.214 17574-17574 CommunicationConfig     com.weinuo.quickcommands             D  获取到电话号码：13618606542
2025-08-05 22:15:50.215 17574-17574 CommunicationConfig     com.weinuo.quickcommands             D  已更新电话号码列表
2025-08-05 22:15:50.216 17574-17574 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:15:50.217 17574-17574 ActivityThread          com.weinuo.quickcommands             D  ComponentInfo{com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity} checkFinished=false 2
2025-08-05 22:15:50.218 17574-17574 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:15:50.224 17574-17574 Autofill Status         com.weinuo.quickcommands             D  Autofill popup isn't shown because autofill is not available.
                                                                                                    
                                                                                                    Did you set up autofill?
                                                                                                    1. Go to Settings > System > Languages&input > Advanced > Autofill Service
                                                                                                    2. Pick a service
                                                                                                    
                                                                                                    Did you add an account?
                                                                                                    1. Go to Settings > System > Languages&input > Advanced
                                                                                                    2. Click on the settings icon next to the Autofill Service
                                                                                                    3. Add your account
2025-08-05 22:15:50.231 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 0 cost 14.411979 refreshRate ******** bit true processName com.weinuo.quickcommands
2025-08-05 22:15:50.304 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity
2025-08-05 22:15:50.333 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:false, surfaceControlChanged:true, transformHintChanged:false, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(1080, 2400), mWidth:1080, mHeight:2400, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:0, installOrientation:0, displayRotation:0, isSurfaceValid:true, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:15:50.334 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             W  updateBlastSurfaceIfNeeded, surfaceSize:Point(1080, 2400), lastSurfaceSize:Point(1080, 2400), format:-1, blastBufferQueue:null
2025-08-05 22:15:50.335 17574-17574 BufferQueueConsumer     com.weinuo.quickcommands             D  [](id:44a600000008,api:0,p:-1,c:17574) connect: controlledByApp=false
2025-08-05 22:15:50.342 17574-18831 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#8(BLAST Consumer)8](id:44a600000008,api:1,p:17574,c:17574) connect: api=1 producerControlledByApp=true
2025-08-05 22:15:50.342 17574-18831 OpenGLRenderer          com.weinuo.quickcommands             E  Unable to match the desired swap behavior.
2025-08-05 22:15:50.343 17574-17574 SurfaceControl          com.weinuo.quickcommands             I   setExtendedRangeBrightness sc=Surface(name=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity)/@0x7cf0778,currentBufferRatio=1.0,desiredRatio=1.0
2025-08-05 22:15:50.383 17574-18831 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#8](f:0,a:1) acquireNextBufferLocked size=1080x2400 mFrameNumber=1 applyTransaction=true mTimestamp=76276992267047(auto) mPendingTransactions.size=0 graphicBufferId=75479755259939 transform=0
2025-08-05 22:15:50.384 17574-18831 VRI[Commun...gActivity] com.weinuo.quickcommands             D  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true syncBuffer=false
2025-08-05 22:15:50.385 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  draw finished.
2025-08-05 22:15:50.385 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  reportDrawFinished
2025-08-05 22:15:50.386 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  setMaxDequeuedBufferCount: 2
2025-08-05 22:15:50.427 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 9 cost 161.31352 refreshRate 16683675 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:50.452 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 1 cost 19.523928 refreshRate 16683722 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:50.463 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             D  onFocusEvent true
2025-08-05 22:15:50.466 17574-17574 WindowOnBackDispatcher  com.weinuo.quickcommands             W  sendCancelIfRunning: isInProgress=falsecallback=ImeCallback=ImeOnBackInvokedCallback@37719889 Callback=android.window.IOnBackInvokedCallback$Stub$Proxy@b0c5bc2
2025-08-05 22:15:50.496 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 29.722893 refreshRate 16683846 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:50.561 17574-17574 RemoteInpu...ectionImpl com.weinuo.quickcommands             W  getTextBeforeCursor on inactive InputConnection
2025-08-05 22:15:50.564 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 63.85161 refreshRate 16683609 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:50.591 17574-17574 WindowOnBackDispatcher  com.weinuo.quickcommands             W  sendCancelIfRunning: isInProgress=falsecallback=ImeCallback=ImeOnBackInvokedCallback@37719889 Callback=android.window.IOnBackInvokedCallback$Stub$Proxy@b0c5bc2
2025-08-05 22:15:50.594 17574-17574 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:c9246408: onCancelled at PHASE_CLIENT_ANIMATION_CANCEL
2025-08-05 22:15:50.598 17574-17574 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:d8ebce3f: onRequestHide at ORIGIN_CLIENT_HIDE_SOFT_INPUT reason HIDE_SOFT_INPUT_BY_INSETS_API
2025-08-05 22:15:50.598 17574-17574 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:d8ebce3f: onFailed at PHASE_CLIENT_VIEW_SERVED
2025-08-05 22:15:50.606 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             W  handleResized abandoned!
2025-08-05 22:15:50.612 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: false 1 cost 28.40895 refreshRate 16685156 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:50.953 17574-18859 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  dispatchAppVisibility visible:false
2025-08-05 22:15:50.972 17574-17574 OpenGLRenderer          com.weinuo.quickcommands             D  RenderProxy::destroy: this=0xb40000788ebc0780, mContext=0xb40000788ed84cc0
2025-08-05 22:15:50.972 17574-18831 OpenGLRenderer          com.weinuo.quickcommands             D  SkiaOpenGLPipeline::setSurface: this=0xb40000788ea545c0, surface=NULL
2025-08-05 22:15:50.974 17574-18831 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[PhoneNumberInputActivity]#7(BLAST Consumer)7](id:44a600000007,api:1,p:17574,c:17574) disconnect: api 1
2025-08-05 22:15:50.976 17574-17574 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity
2025-08-05 22:15:51.000 17574-17574 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:false, surfaceControlChanged:true, transformHintChanged:false, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(1080, 2400), mWidth:1080, mHeight:2400, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:0, installOrientation:0, displayRotation:0, isSurfaceValid:false, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:15:51.001 17574-17574 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[PhoneNumberInputActivity]#7](f:0,a:2) destructor()
2025-08-05 22:15:51.001 17574-17574 BufferQueueConsumer     com.weinuo.quickcommands             D  [VRI[PhoneNumberInputActivity]#7(BLAST Consumer)7](id:44a600000007,api:0,p:-1,c:17574) disconnect
2025-08-05 22:15:51.016 17574-17574 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  setWindowStopped stopped:true
2025-08-05 22:15:51.129 17574-17574 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity,window dying
2025-08-05 22:15:51.129 17574-17574 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity,unregisterSystemUIBroadcastReceiver 
2025-08-05 22:15:51.130 17574-17574 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity, unregisterSystemUIBroadcastReceiver failed java.lang.IllegalArgumentException: Receiver not registered: android.view.OplusScrollToTopManager$2@cd513c5
2025-08-05 22:15:51.133 17574-17574 WindowOnBackDispatcher  com.weinuo.quickcommands             W  sendCancelIfRunning: isInProgress=falsecallback=android.view.ViewRootImpl$$ExternalSyntheticLambda17@ec2e56b
2025-08-05 22:15:51.142 17574-17574 OpenGLRenderer          com.weinuo.quickcommands             D  RenderProxy::destroy: this=0xb40000788ebc0780, mContext=0xb40000788ed84cc0
2025-08-05 22:15:51.142 17574-18831 OpenGLRenderer          com.weinuo.quickcommands             D  SkiaOpenGLPipeline::setSurface: this=0xb40000788ea545c0, surface=NULL
2025-08-05 22:15:51.160 17574-17574 Quality                 com.weinuo.quickcommands             I  Skipped: true 9 cost 160.06969 refreshRate 16689202 bit true processName com.weinuo.quickcommands
2025-08-05 22:15:51.237 17574-17574 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:6c3a0031: onRequestHide at ORIGIN_CLIENT_HIDE_SOFT_INPUT reason HIDE_SOFT_INPUT
2025-08-05 22:15:51.238 17574-17574 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:6c3a0031: onFailed at PHASE_CLIENT_VIEW_SERVED
2025-08-05 22:15:51.244 17574-17574 VRI[Commun...gActivity] com.weinuo.quickcommands             W  handleResized abandoned!
2025-08-05 22:15:51.470 17574-18857 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity,This DecorView@aacc19b[CommunicationStateDetailConfigActivity] change focus to true
""", flush=True)
