# -*- coding: utf-8 -*-
# final_review_gate.py
import sys

if __name__ == "__main__":
    print("""解决问题：2025-08-05 22:18:56.330 21307-21307 ViewRootImplExtImpl     com.weinuo.quickcommands             D  the up motion event handled by client, just return
2025-08-05 22:18:56.341 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 2 cost 47.718185 refreshRate 16684875 bit true processName com.weinuo.quickcommands
2025-08-05 22:18:56.346 21307-21307 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=null, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:18:56.355 21307-21332 VRI[Commun...gActivity] com.weinuo.quickcommands             D  dispatchAppVisibility visible:true
2025-08-05 22:18:56.375 21307-21307 VRI[Contac...nActivity] com.weinuo.quickcommands             D  onFocusEvent false
2025-08-05 22:18:56.379 21307-21307 Compatibil...geReporter com.weinuo.quickcommands             D  Compat change id reported: 78294732; UID 11011; state: ENABLED
2025-08-05 22:18:56.380 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             D  setWindowStopped stopped:false
2025-08-05 22:18:56.390 21307-21307 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:18:56.390 21307-21307 ActivityThread          com.weinuo.quickcommands             D  ComponentInfo{com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity} checkFinished=false 2
2025-08-05 22:18:56.391 21307-21307 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:18:56.420 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 1 cost 26.788088 refreshRate 16684543 bit true processName com.weinuo.quickcommands
2025-08-05 22:18:56.572 21307-21307 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity
2025-08-05 22:18:56.602 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:false, surfaceControlChanged:true, transformHintChanged:false, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(1080, 2400), mWidth:1080, mHeight:2400, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:0, installOrientation:0, displayRotation:0, isSurfaceValid:true, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:18:56.603 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             W  updateBlastSurfaceIfNeeded, surfaceSize:Point(1080, 2400), lastSurfaceSize:Point(1080, 2400), format:-1, blastBufferQueue:null
2025-08-05 22:18:56.603 21307-21307 BufferQueueConsumer     com.weinuo.quickcommands             D  [](id:533b00000006,api:0,p:-1,c:21307) connect: controlledByApp=false
2025-08-05 22:18:56.607 21307-21348 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#6(BLAST Consumer)6](id:533b00000006,api:1,p:21307,c:21307) connect: api=1 producerControlledByApp=true
2025-08-05 22:18:56.607 21307-21348 OpenGLRenderer          com.weinuo.quickcommands             E  Unable to match the desired swap behavior.
2025-08-05 22:18:56.630 21307-21307 SurfaceControl          com.weinuo.quickcommands             I   setExtendedRangeBrightness sc=Surface(name=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity)/@0xc56f801,currentBufferRatio=1.0,desiredRatio=1.0
2025-08-05 22:18:56.680 21307-21348 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#6](f:0,a:1) acquireNextBufferLocked size=1080x2400 mFrameNumber=1 applyTransaction=true mTimestamp=76463288491194(auto) mPendingTransactions.size=0 graphicBufferId=91512868175899 transform=0
2025-08-05 22:18:56.681 21307-21348 VRI[Commun...gActivity] com.weinuo.quickcommands             D  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true syncBuffer=false
2025-08-05 22:18:56.682 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             D  draw finished.
2025-08-05 22:18:56.682 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             D  reportDrawFinished
2025-08-05 22:18:56.683 21307-21307 ViewRootImplExtImpl     com.weinuo.quickcommands             D  setMaxDequeuedBufferCount: 2
2025-08-05 22:18:56.698 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 15 cost 254.65431 refreshRate 16684543 bit true processName com.weinuo.quickcommands
2025-08-05 22:18:56.750 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             D  onFocusEvent true
2025-08-05 22:18:57.253 21307-21328 VRI[Contac...nActivity] com.weinuo.quickcommands             D  dispatchAppVisibility visible:false
2025-08-05 22:18:57.264 21307-21307 OpenGLRenderer          com.weinuo.quickcommands             D  RenderProxy::destroy: this=0xb4000078a1458580, mContext=0xb40000790b57ba80
2025-08-05 22:18:57.264 21307-21348 OpenGLRenderer          com.weinuo.quickcommands             D  SkiaOpenGLPipeline::setSurface: this=0xb40000790e691b40, surface=NULL
2025-08-05 22:18:57.264 21307-21348 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[ContactRangeSelectionActivity]#5(BLAST Consumer)5](id:533b00000005,api:1,p:21307,c:21307) disconnect: api 1
2025-08-05 22:18:57.266 21307-21307 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity
2025-08-05 22:18:57.288 21307-21307 VRI[Contac...nActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:false, surfaceControlChanged:true, transformHintChanged:false, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(1080, 2400), mWidth:1080, mHeight:2400, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:0, installOrientation:0, displayRotation:0, isSurfaceValid:false, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:18:57.288 21307-21307 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[ContactRangeSelectionActivity]#5](f:0,a:1) destructor()
2025-08-05 22:18:57.288 21307-21307 BufferQueueConsumer     com.weinuo.quickcommands             D  [VRI[ContactRangeSelectionActivity]#5(BLAST Consumer)5](id:533b00000005,api:0,p:-1,c:21307) disconnect
2025-08-05 22:18:57.293 21307-21307 VRI[Contac...nActivity] com.weinuo.quickcommands             D  setWindowStopped stopped:true
2025-08-05 22:18:57.375 21307-21307 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity,window dying
2025-08-05 22:18:57.375 21307-21307 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity,unregisterSystemUIBroadcastReceiver 
2025-08-05 22:18:57.376 21307-21307 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.ContactRangeSelectionActivity, unregisterSystemUIBroadcastReceiver failed java.lang.IllegalArgumentException: Receiver not registered: android.view.OplusScrollToTopManager$2@78ee0ea
2025-08-05 22:18:57.380 21307-21307 WindowOnBackDispatcher  com.weinuo.quickcommands             W  sendCancelIfRunning: isInProgress=falsecallback=android.view.ViewRootImpl$$ExternalSyntheticLambda17@acbb287
2025-08-05 22:18:57.394 21307-21307 OpenGLRenderer          com.weinuo.quickcommands             D  RenderProxy::destroy: this=0xb4000078a1458580, mContext=0xb40000790b57ba80
2025-08-05 22:18:57.394 21307-21348 OpenGLRenderer          com.weinuo.quickcommands             D  SkiaOpenGLPipeline::setSurface: this=0xb40000790e691b40, surface=NULL
2025-08-05 22:18:57.413 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             W  handleResized abandoned!
2025-08-05 22:18:57.448 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 8 cost 137.18123 refreshRate 16684358 bit true processName com.weinuo.quickcommands
2025-08-05 22:18:57.759 21307-21409 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity,This DecorView@8486ba8[CommunicationStateDetailConfigActivity] change focus to true
2025-08-05 22:18:58.617 21307-21307 ViewRootImplExtImpl     com.weinuo.quickcommands             D  the up motion event handled by client, just return
2025-08-05 22:18:58.629 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 2 cost 33.85511 refreshRate 16683032 bit true processName com.weinuo.quickcommands
2025-08-05 22:18:58.635 21307-21307 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=null, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:18:58.676 21307-21307 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:18:58.764 21307-21307 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:18:58.764 21307-21307 ActivityThread          com.weinuo.quickcommands             D  ComponentInfo{com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity} checkFinished=false 2
2025-08-05 22:18:58.765 21307-21307 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:18:58.780 21307-21307 WindowManager           com.weinuo.quickcommands             D  Add to mViews: DecorView@ffa03a7[PhoneNumberInputActivity],pkg= com.weinuo.quickcommands
2025-08-05 22:18:58.805 21307-21307 InputEventReceiver      com.weinuo.quickcommands             D  Input log is disabled in InputEventReceiver.
2025-08-05 22:18:58.809 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             D  onFocusEvent false
2025-08-05 22:18:58.811 21307-21307 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:18:58.815 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 51.42255 refreshRate 16683560 bit true processName com.weinuo.quickcommands
2025-08-05 22:18:58.816 21307-21307 OplusBracketLog         com.weinuo.quickcommands             E  [OplusViewMirrorManager] updateHostViewRootIfNeeded, not support android.view.ViewRootImpl@60260f2
2025-08-05 22:18:58.833 21307-21307 Vibrator                com.weinuo.quickcommands             D  SystemVibrator Created
2025-08-05 22:18:58.838 21307-21307 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:18:58.866 21307-21307 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:18:58.871 21307-21307 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:18:58.918 21307-21307 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:18:58.919 21307-21307 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:18:58.922 21307-21307 SettingsRepository      com.weinuo.quickcommands             D  Loading global settings: experimentalFeaturesEnabled=false, experimentalFeaturesUnlocked=false
2025-08-05 22:18:58.966 21307-21307 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity
2025-08-05 22:18:58.990 21307-21307 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:true, surfaceControlChanged:true, transformHintChanged:true, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(0, 0), mWidth:-1, mHeight:-1, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:-2147483648, installOrientation:0, displayRotation:0, isSurfaceValid:true, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:18:58.990 21307-21307 VRI[PhoneN...tActivity] com.weinuo.quickcommands             W  updateBlastSurfaceIfNeeded, surfaceSize:Point(1080, 2400), lastSurfaceSize:Point(0, 0), format:-1, blastBufferQueue:null
2025-08-05 22:18:58.990 21307-21307 BufferQueueConsumer     com.weinuo.quickcommands             D  [](id:533b00000007,api:0,p:-1,c:21307) connect: controlledByApp=false
2025-08-05 22:18:58.995 21307-21348 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[PhoneNumberInputActivity]#7(BLAST Consumer)7](id:533b00000007,api:1,p:21307,c:21307) connect: api=1 producerControlledByApp=true
2025-08-05 22:18:58.995 21307-21348 OpenGLRenderer          com.weinuo.quickcommands             E  Unable to match the desired swap behavior.
2025-08-05 22:18:59.013 21307-21307 SurfaceControl          com.weinuo.quickcommands             I   setExtendedRangeBrightness sc=Surface(name=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity)/@0x577a73,currentBufferRatio=1.0,desiredRatio=1.0
2025-08-05 22:18:59.034 21307-21348 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[PhoneNumberInputActivity]#7](f:0,a:1) acquireNextBufferLocked size=1080x2400 mFrameNumber=1 applyTransaction=true mTimestamp=76465643223016(auto) mPendingTransactions.size=0 graphicBufferId=91512868175903 transform=0
2025-08-05 22:18:59.035 21307-21348 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true syncBuffer=false
2025-08-05 22:18:59.036 21307-21307 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  draw finished.
2025-08-05 22:18:59.036 21307-21307 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  reportDrawFinished
2025-08-05 22:18:59.036 21307-21307 ViewRootImplExtImpl     com.weinuo.quickcommands             D  setMaxDequeuedBufferCount: 2
2025-08-05 22:18:59.040 21307-21307 AppThemeProvider        com.weinuo.quickcommands             D  === 当前主题调试信息 ===
2025-08-05 22:18:59.041 21307-21307 AppThemeProvider        com.weinuo.quickcommands             D  主题ID: sky_blue
2025-08-05 22:18:59.041 21307-21307 AppThemeProvider        com.weinuo.quickcommands             D  主题名称: 天空蓝
2025-08-05 22:18:59.041 21307-21307 AppThemeProvider        com.weinuo.quickcommands             D  设计方法: INTEGRATED_DESIGN
2025-08-05 22:18:59.041 21307-21307 AppThemeProvider        com.weinuo.quickcommands             D  支持模糊: true
2025-08-05 22:18:59.041 21307-21307 AppThemeProvider        com.weinuo.quickcommands             D  支持阴影: false
2025-08-05 22:18:59.041 21307-21307 AppThemeProvider        com.weinuo.quickcommands             D  支持透明: true
2025-08-05 22:18:59.041 21307-21307 AppThemeProvider        com.weinuo.quickcommands             D  ========================
2025-08-05 22:18:59.041 21307-21307 SkyBlueColorConfig      com.weinuo.quickcommands             D  === 颜色配置读取 ===
2025-08-05 22:18:59.041 21307-21307 SkyBlueColorConfig      com.weinuo.quickcommands             D  Primary: 0xFF0A59F7
2025-08-05 22:18:59.041 21307-21307 SkyBlueColorConfig      com.weinuo.quickcommands             D  Background: 0xFFF1F3F5
2025-08-05 22:18:59.041 21307-21307 SkyBlueColorConfig      com.weinuo.quickcommands             D  Surface: 0xFFF1F3F5
2025-08-05 22:18:59.041 21307-21307 SkyBlueColorConfig      com.weinuo.quickcommands             D  ==================
2025-08-05 22:18:59.042 21307-21307 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  === 天空蓝动态颜色配置 ===
2025-08-05 22:18:59.042 21307-21307 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  Primary: Color(0.039215688, 0.34901962, 0.96862745, 1.0, sRGB IEC61966-2.1)
2025-08-05 22:18:59.042 21307-21307 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  Background: Color(0.94509804, 0.9529412, 0.9607843, 1.0, sRGB IEC61966-2.1)
2025-08-05 22:18:59.042 21307-21307 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  Surface: Color(0.94509804, 0.9529412, 0.9607843, 1.0, sRGB IEC61966-2.1)
2025-08-05 22:18:59.042 21307-21307 SkyBlueDynamicTheme     com.weinuo.quickcommands             D  ========================
2025-08-05 22:18:59.050 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 13 cost 221.66376 refreshRate 16683514 bit true processName com.weinuo.quickcommands
2025-08-05 22:18:59.068 21307-21307 VRI[PhoneN...tActivity] com.weinuo.quickcommands             W  handleResized, msg:5, frameChanged:false, configChanged:false, displayChanged:false, attachedFrameChanged:false, compatScaleChanged:false, pendingDragResizing=false
2025-08-05 22:18:59.077 21307-21348 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  Received frameCommittedCallback lastAttemptedDrawFrameNum=3 didProduceBuffer=false syncBuffer=false
2025-08-05 22:18:59.078 21307-21307 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  draw finished.
2025-08-05 22:18:59.078 21307-21307 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  reportDrawFinished
2025-08-05 22:18:59.217 21307-21307 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  onFocusEvent true
2025-08-05 22:18:59.294 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 31.212141 refreshRate 16684221 bit true processName com.weinuo.quickcommands
2025-08-05 22:18:59.601 21307-21329 VRI[Commun...gActivity] com.weinuo.quickcommands             D  dispatchAppVisibility visible:false
2025-08-05 22:18:59.618 21307-21307 OpenGLRenderer          com.weinuo.quickcommands             D  RenderProxy::destroy: this=0xb4000078a1457400, mContext=0xb40000790b533940
2025-08-05 22:18:59.618 21307-21348 OpenGLRenderer          com.weinuo.quickcommands             D  SkiaOpenGLPipeline::setSurface: this=0xb40000790e684a80, surface=NULL
2025-08-05 22:18:59.619 21307-21348 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#6(BLAST Consumer)6](id:533b00000006,api:1,p:21307,c:21307) disconnect: api 1
2025-08-05 22:18:59.621 21307-21307 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity
2025-08-05 22:18:59.642 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:false, surfaceControlChanged:true, transformHintChanged:false, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(1080, 2400), mWidth:1080, mHeight:2400, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:0, installOrientation:0, displayRotation:0, isSurfaceValid:false, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:18:59.643 21307-21307 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#6](f:0,a:1) destructor()
2025-08-05 22:18:59.643 21307-21307 BufferQueueConsumer     com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#6(BLAST Consumer)6](id:533b00000006,api:0,p:-1,c:21307) disconnect
2025-08-05 22:18:59.648 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             D  setWindowStopped stopped:true
2025-08-05 22:18:59.912 21307-21307 ViewRootImplExtImpl     com.weinuo.quickcommands             D  the up motion event handled by client, just return
2025-08-05 22:18:59.986 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 9 cost 156.04063 refreshRate 16682895 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:00.040 21307-21409 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity,This DecorView@8486ba8[CommunicationStateDetailConfigActivity] change focus to false
2025-08-05 22:19:00.081 21307-21307 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:6c618eef: onRequestShow at ORIGIN_CLIENT_SHOW_SOFT_INPUT reason SHOW_SOFT_INPUT_BY_INSETS_API
2025-08-05 22:19:00.081 21307-21307 InsetsController        com.weinuo.quickcommands             D  show(ime(), fromIme=false)
2025-08-05 22:19:00.086 21307-21307 InputMethodManager      com.weinuo.quickcommands             D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{f59a5ec VFED..... .F....ID 0,0-1080,2400 aid=1073741824 viewInfo = } flags=0 reason=SHOW_SOFT_INPUT_BY_INSETS_API
2025-08-05 22:19:00.158 21307-21307 ViewExtract             com.weinuo.quickcommands             D  [OplusViewExtractManager] OplusViewExtractManager initcom.weinuo.quickcommands
2025-08-05 22:19:00.183 21307-21307 AssistStructure         com.weinuo.quickcommands             I  Flattened final assist data: 2684 bytes, containing 1 windows, 7 views
2025-08-05 22:19:00.204 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 12 cost 207.17778 refreshRate 16682895 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:00.214 21307-21307 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:433838db: onRequestShow at ORIGIN_CLIENT_SHOW_SOFT_INPUT reason SHOW_SOFT_INPUT
2025-08-05 22:19:00.215 21307-21307 InputMethodManager      com.weinuo.quickcommands             D  showSoftInput() view=androidx.compose.ui.platform.AndroidComposeView{f59a5ec VFED..... .F...... 0,0-1080,2400 aid=1073741824 viewInfo = } flags=0 reason=SHOW_SOFT_INPUT
2025-08-05 22:19:00.220 21307-21409 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity,This DecorView@ffa03a7[PhoneNumberInputActivity] change focus to true
2025-08-05 22:19:00.221 21307-21307 VRI[PhoneN...tActivity] com.weinuo.quickcommands             W  handleResized abandoned!
2025-08-05 22:19:00.222 21307-21307 InsetsController        com.weinuo.quickcommands             D  show(ime(), fromIme=true)
2025-08-05 22:19:00.249 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 18.230911 refreshRate 16682353 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:00.254 21307-21307 InsetsController        com.weinuo.quickcommands             D  show(ime(), fromIme=true)
2025-08-05 22:19:00.256 21307-21307 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:433838db: onCancelled at PHASE_CLIENT_APPLY_ANIMATION
2025-08-05 22:19:00.265 21307-21307 VRI[PhoneN...tActivity] com.weinuo.quickcommands             W  handleResized abandoned!
2025-08-05 22:19:00.556 21307-21307 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:6c618eef: onShown
2025-08-05 22:19:01.125 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 4 cost 77.08945 refreshRate 16684784 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:01.332 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 50.309975 refreshRate 16684728 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:01.556 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 23.901539 refreshRate 16684650 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:01.656 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 57.53971 refreshRate 16684828 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:01.850 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 18.100838 refreshRate 16684203 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:01.934 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 51.833755 refreshRate 16684769 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:02.131 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 32.074932 refreshRate 16684077 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:02.220 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 53.59695 refreshRate 16684215 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:02.553 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 19.908953 refreshRate 16684233 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:02.655 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 55.23937 refreshRate 16683983 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:03.000 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: true 2 cost 33.404957 refreshRate 16683840 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:03.102 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 52.0498 refreshRate 16683971 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:03.225 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 24.926435 refreshRate 16683770 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:03.327 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 60.283375 refreshRate 16683805 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:03.593 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 59.16552 refreshRate 16683601 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:03.718 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 17.037767 refreshRate 16684034 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:03.804 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 53.301216 refreshRate 16684060 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:03.905 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: true 1 cost 21.173254 refreshRate 16684045 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:04.003 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 52.332455 refreshRate 16683968 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:04.379 21307-21307 PhoneNumberInput        com.weinuo.quickcommands             D  保存按钮点击，号码：13610353555
2025-08-05 22:19:04.380 21307-21307 PhoneNumberInput        com.weinuo.quickcommands             D  onNumberChanged调用，号码：13610353555
2025-08-05 22:19:04.397 21307-21307 ViewRootImplExtImpl     com.weinuo.quickcommands             D  the up motion event handled by client, just return
2025-08-05 22:19:04.399 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: true 2 cost 47.795567 refreshRate 16683728 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:04.420 21307-21307 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=null, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:19:04.428 21307-21332 VRI[Commun...gActivity] com.weinuo.quickcommands             D  dispatchAppVisibility visible:true
2025-08-05 22:19:04.448 21307-21307 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  onFocusEvent false
2025-08-05 22:19:04.464 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             D  setWindowStopped stopped:false
2025-08-05 22:19:04.470 21307-21307 CommunicationConfig     com.weinuo.quickcommands             D  phoneNumberInputLauncher结果：resultCode=-1
2025-08-05 22:19:04.470 21307-21307 CommunicationConfig     com.weinuo.quickcommands             D  获取到电话号码：13610353555
2025-08-05 22:19:04.471 21307-21307 CommunicationConfig     com.weinuo.quickcommands             D  updateSelectedPhoneNumbers调用，号码列表：[13610353555]
2025-08-05 22:19:04.472 21307-21307 CommunicationConfig     com.weinuo.quickcommands             D  selectedPhoneNumbers更新后：[13610353555]
2025-08-05 22:19:04.472 21307-21307 CommunicationConfig     com.weinuo.quickcommands             D  已更新电话号码列表
2025-08-05 22:19:04.473 21307-21307 ScrollOpti...neManager] com.weinuo.quickcommands             D  updateCurrentActivity: mCurrentActivityName=com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity, isOptEnable=true, isAnimAheadEnable=true, isFrameInsertEnable=true, InsertNum=1, isEnabledForScrollChanged=false
2025-08-05 22:19:04.474 21307-21307 ActivityThread          com.weinuo.quickcommands             D  ComponentInfo{com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity} checkFinished=false 2
2025-08-05 22:19:04.475 21307-21307 ResourcesManagerExtImpl com.weinuo.quickcommands             D  applyConfigurationToAppResourcesLocked app.getDisplayId() return callback.displayId:-1
2025-08-05 22:19:04.482 21307-21307 Autofill Status         com.weinuo.quickcommands             D  Autofill popup isn't shown because autofill is not available.
                                                                                                    
                                                                                                    Did you set up autofill?
                                                                                                    1. Go to Settings > System > Languages&input > Advanced > Autofill Service
                                                                                                    2. Pick a service
                                                                                                    
                                                                                                    Did you add an account?
                                                                                                    1. Go to Settings > System > Languages&input > Advanced
                                                                                                    2. Click on the settings icon next to the Autofill Service
                                                                                                    3. Add your account
2025-08-05 22:19:04.488 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 0 cost 14.521823 refreshRate ******** bit true processName com.weinuo.quickcommands
2025-08-05 22:19:04.572 21307-21307 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity
2025-08-05 22:19:04.600 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:false, surfaceControlChanged:true, transformHintChanged:false, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(1080, 2400), mWidth:1080, mHeight:2400, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:0, installOrientation:0, displayRotation:0, isSurfaceValid:true, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:19:04.601 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             W  updateBlastSurfaceIfNeeded, surfaceSize:Point(1080, 2400), lastSurfaceSize:Point(1080, 2400), format:-1, blastBufferQueue:null
2025-08-05 22:19:04.602 21307-21307 BufferQueueConsumer     com.weinuo.quickcommands             D  [](id:533b00000008,api:0,p:-1,c:21307) connect: controlledByApp=false
2025-08-05 22:19:04.609 21307-21348 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#8(BLAST Consumer)8](id:533b00000008,api:1,p:21307,c:21307) connect: api=1 producerControlledByApp=true
2025-08-05 22:19:04.609 21307-21348 OpenGLRenderer          com.weinuo.quickcommands             E  Unable to match the desired swap behavior.
2025-08-05 22:19:04.610 21307-21307 SurfaceControl          com.weinuo.quickcommands             I   setExtendedRangeBrightness sc=Surface(name=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity)/@0xc56f801,currentBufferRatio=1.0,desiredRatio=1.0
2025-08-05 22:19:04.651 21307-21348 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[CommunicationStateDetailConfigActivity]#8](f:0,a:1) acquireNextBufferLocked size=1080x2400 mFrameNumber=1 applyTransaction=true mTimestamp=76471260160983(auto) mPendingTransactions.size=0 graphicBufferId=91512868175907 transform=0
2025-08-05 22:19:04.652 21307-21348 VRI[Commun...gActivity] com.weinuo.quickcommands             D  Received frameCommittedCallback lastAttemptedDrawFrameNum=1 didProduceBuffer=true syncBuffer=false
2025-08-05 22:19:04.653 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             D  draw finished.
2025-08-05 22:19:04.653 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             D  reportDrawFinished
2025-08-05 22:19:04.655 21307-21307 ViewRootImplExtImpl     com.weinuo.quickcommands             D  setMaxDequeuedBufferCount: 2
2025-08-05 22:19:04.684 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 10 cost 182.31363 refreshRate 16683572 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:04.713 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             D  onFocusEvent true
2025-08-05 22:19:04.719 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 1 cost 17.206247 refreshRate 16684125 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:04.730 21307-21307 WindowOnBackDispatcher  com.weinuo.quickcommands             W  sendCancelIfRunning: isInProgress=falsecallback=ImeCallback=ImeOnBackInvokedCallback@37719889 Callback=android.window.IOnBackInvokedCallback$Stub$Proxy@97ce75
2025-08-05 22:19:04.813 21307-21307 RemoteInpu...ectionImpl com.weinuo.quickcommands             W  getTextBeforeCursor on inactive InputConnection
2025-08-05 22:19:04.823 21307-21307 WindowOnBackDispatcher  com.weinuo.quickcommands             W  sendCancelIfRunning: isInProgress=falsecallback=ImeCallback=ImeOnBackInvokedCallback@37719889 Callback=android.window.IOnBackInvokedCallback$Stub$Proxy@97ce75
2025-08-05 22:19:04.832 21307-21307 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:6f6c38e2: onCancelled at PHASE_CLIENT_ANIMATION_CANCEL
2025-08-05 22:19:04.836 21307-21307 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:ae24b14d: onRequestHide at ORIGIN_CLIENT_HIDE_SOFT_INPUT reason HIDE_SOFT_INPUT_BY_INSETS_API
2025-08-05 22:19:04.838 21307-21307 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:ae24b14d: onFailed at PHASE_CLIENT_VIEW_SERVED
2025-08-05 22:19:04.852 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: false 3 cost 66.40816 refreshRate 16684294 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:04.866 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             W  handleResized abandoned!
2025-08-05 22:19:05.210 21307-21332 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  dispatchAppVisibility visible:false
2025-08-05 22:19:05.223 21307-21307 OpenGLRenderer          com.weinuo.quickcommands             D  RenderProxy::destroy: this=0xb4000078a1458d00, mContext=0xb40000790b5a1ac0
2025-08-05 22:19:05.223 21307-21348 OpenGLRenderer          com.weinuo.quickcommands             D  SkiaOpenGLPipeline::setSurface: this=0xb40000790e691f00, surface=NULL
2025-08-05 22:19:05.224 21307-21348 BufferQueueProducer     com.weinuo.quickcommands             D  [VRI[PhoneNumberInputActivity]#7(BLAST Consumer)7](id:533b00000007,api:1,p:21307,c:21307) disconnect: api 1
2025-08-05 22:19:05.226 21307-21307 ViewRootImplExtImpl     com.weinuo.quickcommands             D  wrapConfigInfoIntoFlags rotation=0, smallestScreenWidthDp=360, relayoutAsync=false, newFlags=********, title=com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity
2025-08-05 22:19:05.247 21307-21307 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  relayoutWindow result, sizeChanged:false, surfaceControlChanged:true, transformHintChanged:false, mSurfaceSize:Point(1080, 2400), mLastSurfaceSize:Point(1080, 2400), mWidth:1080, mHeight:2400, requestedWidth:1080, requestedHeight:2400, transformHint:0, lastTransformHint:0, installOrientation:0, displayRotation:0, isSurfaceValid:false, attr.flag:-**********, useBlast:true, tmpFrames:ClientWindowFrames{frame=[0,0][1080,2400] display=[0,0][1080,2400] parentFrame=[0,0][0,0]}
2025-08-05 22:19:05.248 21307-21307 BLASTBufferQueue        com.weinuo.quickcommands             D  [VRI[PhoneNumberInputActivity]#7](f:0,a:2) destructor()
2025-08-05 22:19:05.248 21307-21307 BufferQueueConsumer     com.weinuo.quickcommands             D  [VRI[PhoneNumberInputActivity]#7(BLAST Consumer)7](id:533b00000007,api:0,p:-1,c:21307) disconnect
2025-08-05 22:19:05.263 21307-21307 VRI[PhoneN...tActivity] com.weinuo.quickcommands             D  setWindowStopped stopped:true
2025-08-05 22:19:05.378 21307-21307 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity,window dying
2025-08-05 22:19:05.378 21307-21307 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity,unregisterSystemUIBroadcastReceiver 
2025-08-05 22:19:05.379 21307-21307 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.PhoneNumberInputActivity, unregisterSystemUIBroadcastReceiver failed java.lang.IllegalArgumentException: Receiver not registered: android.view.OplusScrollToTopManager$2@87caf57
2025-08-05 22:19:05.383 21307-21307 WindowOnBackDispatcher  com.weinuo.quickcommands             W  sendCancelIfRunning: isInProgress=falsecallback=android.view.ViewRootImpl$$ExternalSyntheticLambda17@aa26202
2025-08-05 22:19:05.393 21307-21307 OpenGLRenderer          com.weinuo.quickcommands             D  RenderProxy::destroy: this=0xb4000078a1458d00, mContext=0xb40000790b5a1ac0
2025-08-05 22:19:05.393 21307-21348 OpenGLRenderer          com.weinuo.quickcommands             D  SkiaOpenGLPipeline::setSurface: this=0xb40000790e691f00, surface=NULL
2025-08-05 22:19:05.411 21307-21307 Quality                 com.weinuo.quickcommands             I  Skipped: true 9 cost 158.9574 refreshRate 16690130 bit true processName com.weinuo.quickcommands
2025-08-05 22:19:05.482 21307-21307 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:2134e5c2: onRequestHide at ORIGIN_CLIENT_HIDE_SOFT_INPUT reason HIDE_SOFT_INPUT
2025-08-05 22:19:05.483 21307-21307 ImeTracker              com.weinuo.quickcommands             I  com.weinuo.quickcommands:2134e5c2: onFailed at PHASE_CLIENT_VIEW_SERVED
2025-08-05 22:19:05.489 21307-21307 VRI[Commun...gActivity] com.weinuo.quickcommands             W  handleResized abandoned!
2025-08-05 22:19:05.732 21307-21409 OplusScrollToTopManager com.weinuo.quickcommands             D  com.weinuo.quickcommands/com.weinuo.quickcommands.ui.activities.CommunicationStateDetailConfigActivity,This DecorView@8486ba8[CommunicationStateDetailConfigActivity] change focus to true
""", flush=True)
