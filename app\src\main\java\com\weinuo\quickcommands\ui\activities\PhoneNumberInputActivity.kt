package com.weinuo.quickcommands.ui.activities

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import com.weinuo.quickcommands.ui.theme.QuickCommandsTheme
import com.weinuo.quickcommands.ui.theme.manager.ThemeManager
import com.weinuo.quickcommands.ui.components.skyblue.SkyBlueBackButton
import com.weinuo.quickcommands.ui.components.IOSStyleCardInput

/**
 * 电话号码输入Activity
 */
class PhoneNumberInputActivity : ComponentActivity() {

    companion object {
        const val EXTRA_CURRENT_NUMBER = "current_number"
        const val RESULT_PHONE_NUMBER = "phone_number_result"

        fun startForResult(context: Context, currentNumber: String = "") {
            val intent = Intent(context, PhoneNumberInputActivity::class.java).apply {
                putExtra(EXTRA_CURRENT_NUMBER, currentNumber)
            }
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        val currentNumber = intent.getStringExtra(EXTRA_CURRENT_NUMBER) ?: ""

        setContent {
            QuickCommandsTheme {
                PhoneNumberInputScreen(
                    currentNumber = currentNumber,
                    onNumberChanged = { number ->
                        // 返回输入结果
                        android.util.Log.d("PhoneNumberInput", "onNumberChanged调用，号码：$number")
                        val resultIntent = Intent().apply {
                            putExtra(RESULT_PHONE_NUMBER, number)
                        }
                        setResult(RESULT_OK, resultIntent)
                        finish()
                    },
                    onNavigateBack = {
                        finish()
                    }
                )
            }
        }
    }
}

/**
 * 电话号码输入界面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PhoneNumberInputScreen(
    currentNumber: String,
    onNumberChanged: (String) -> Unit,
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    val themeManager = remember { ThemeManager.getInstance(context) }
    var phoneNumber by remember { mutableStateOf(currentNumber) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "电话号码",
                        style = MaterialTheme.typography.titleLarge
                    )
                },
                navigationIcon = {
                    if (themeManager.getCurrentThemeId() == "sky_blue") {
                        SkyBlueBackButton(onClick = onNavigateBack)
                    } else {
                        IconButton(onClick = onNavigateBack) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "返回"
                            )
                        }
                    }
                },
                actions = {
                    // 保存按钮
                    TextButton(
                        onClick = {
                            android.util.Log.d("PhoneNumberInput", "保存按钮点击，号码：$phoneNumber")
                            onNumberChanged(phoneNumber.trim())
                        },
                        enabled = phoneNumber.isNotBlank()
                    ) {
                        Text("保存")
                    }
                }
            )
        }
    ) { paddingValues ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            item {
                // 使用新的卡片输入组件
                IOSStyleCardInput(
                    value = phoneNumber,
                    onValueChange = { phoneNumber = it },
                    placeholder = "例如：13800138000",
                    keyboardType = androidx.compose.ui.text.input.KeyboardType.Phone,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}
