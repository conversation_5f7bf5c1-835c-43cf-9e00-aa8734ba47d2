/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity android.view.View android.app.Service kotlin.Enum android.app.Service kotlin.Enum kotlin.Enum android.app.Service android.view.View android.widget.FrameLayout android.view.View kotlin.Enum kotlin.Enum4 3com.weinuo.quickcommands.model.AdvancedMemoryConfig4 3com.weinuo.quickcommands.model.AdvancedMemoryConfig4 3com.weinuo.quickcommands.model.AdvancedMemoryConfig4 3com.weinuo.quickcommands.model.AdvancedMemoryConfig kotlin.Enum android.os.Parcelable kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum* )com.weinuo.quickcommands.model.SharedTask kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum* )com.weinuo.quickcommands.model.SharedTask kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum* )com.weinuo.quickcommands.model.SharedTask kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum* )com.weinuo.quickcommands.model.SharedTask kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum* )com.weinuo.quickcommands.model.SharedTask* )com.weinuo.quickcommands.model.SharedTask* )com.weinuo.quickcommands.model.SharedTask* )com.weinuo.quickcommands.model.SharedTask kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum* )com.weinuo.quickcommands.model.SharedTask kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum* )com.weinuo.quickcommands.model.SharedTask kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum* )com.weinuo.quickcommands.model.SharedTask kotlin.Enum kotlin.Enum* )com.weinuo.quickcommands.model.SharedTask kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum* )com.weinuo.quickcommands.model.SharedTask kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum* )com.weinuo.quickcommands.model.SharedTask kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum6 5com.weinuo.quickcommands.model.SharedTriggerCondition kotlin.Enum kotlin.Enum6 5com.weinuo.quickcommands.model.SharedTriggerCondition kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum6 5com.weinuo.quickcommands.model.SharedTriggerCondition6 5com.weinuo.quickcommands.model.SharedTriggerCondition6 5com.weinuo.quickcommands.model.SharedTriggerCondition kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum6 5com.weinuo.quickcommands.model.SharedTriggerCondition kotlin.Enum kotlin.Enum kotlin.Enum6 5com.weinuo.quickcommands.model.SharedTriggerCondition6 5com.weinuo.quickcommands.model.SharedTriggerCondition kotlin.Enum+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen& %android.app.admin.DeviceAdminReceiver kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum" !android.content.BroadcastReceiver android.app.Service2 1android.accessibilityservice.AccessibilityService android.app.Service android.app.Service2 1android.accessibilityservice.AccessibilityService2 1android.accessibilityservice.AccessibilityService9 8android.service.notification.NotificationListenerService android.app.Service android.app.Service2 1android.accessibilityservice.AccessibilityService2 1android.accessibilityservice.AccessibilityService android.app.Service$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum? >com.weinuo.quickcommands.storage.adapters.BaseConditionAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapterB Acom.weinuo.quickcommands.storage.adapters.ConditionStorageAdapter= <com.weinuo.quickcommands.storage.adapters.TaskStorageAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter? >com.weinuo.quickcommands.storage.adapters.BaseConditionAdapter? >com.weinuo.quickcommands.storage.adapters.BaseConditionAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter? >com.weinuo.quickcommands.storage.adapters.BaseConditionAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter? >com.weinuo.quickcommands.storage.adapters.BaseConditionAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter? >com.weinuo.quickcommands.storage.adapters.BaseConditionAdapter? >com.weinuo.quickcommands.storage.adapters.BaseConditionAdapter: 9com.weinuo.quickcommands.storage.adapters.BaseTaskAdapter$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity android.view.View5 4android.view.GestureDetector.SimpleOnGestureListener? >android.view.ScaleGestureDetector.SimpleOnScaleGestureListener kotlin.Enum kotlin.Enum kotlin.Enum3 2androidx.compose.material3.TopAppBarScrollBehavior kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum2 1com.weinuo.quickcommands.ui.screens.ScreenFactory kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum2 1com.weinuo.quickcommands.ui.screens.ScreenFactory kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum, +androidx.lifecycle.DefaultLifecycleObserver, +androidx.lifecycle.DefaultLifecycleObserver@ ?com.weinuo.quickcommands.ui.theme.system.AnimationConfigurationC Bcom.weinuo.quickcommands.ui.theme.system.HazeAwareComponentFactoryB Acom.weinuo.quickcommands.ui.theme.system.InteractionConfiguration< ;com.weinuo.quickcommands.ui.theme.system.StyleConfiguration7 6com.weinuo.quickcommands.ui.theme.system.ThemeProvider@ ?com.weinuo.quickcommands.ui.theme.system.AnimationConfiguration kotlin.Enum kotlin.Enum kotlin.EnumC Bcom.weinuo.quickcommands.ui.theme.system.HazeAwareComponentFactoryB Acom.weinuo.quickcommands.ui.theme.system.InteractionConfiguration kotlin.Enum< ;com.weinuo.quickcommands.ui.theme.system.StyleConfiguration kotlin.Enum7 6com.weinuo.quickcommands.ui.theme.system.ThemeProvider kotlin.Enum: 9com.weinuo.quickcommands.ui.theme.system.ComponentFactory kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum android.os.Parcelable" android.os.Parcelablekotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel$ #android.appwidget.AppWidgetProvider$ #android.appwidget.AppWidgetProvider$ #android.appwidget.AppWidgetProvider$ #android.appwidget.AppWidgetProvider$ #androidx.activity.ComponentActivity kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum$ #androidx.activity.ComponentActivity+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum android.view.View kotlin.Enum$ #androidx.activity.ComponentActivity kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum< ;com.weinuo.quickcommands.ui.theme.system.StyleConfiguration kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum android.view.View android.view.View kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen+ *com.weinuo.quickcommands.navigation.Screen kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum$ #androidx.activity.ComponentActivity kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum android.view.View kotlin.Enum$ #androidx.activity.ComponentActivity kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum< ;com.weinuo.quickcommands.ui.theme.system.StyleConfiguration kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum android.view.View kotlin.Enum$ #androidx.activity.ComponentActivity kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum< ;com.weinuo.quickcommands.ui.theme.system.StyleConfiguration kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum$ #androidx.activity.ComponentActivity kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum$ #androidx.activity.ComponentActivity android.view.View kotlin.Enum$ #androidx.activity.ComponentActivity kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum< ;com.weinuo.quickcommands.ui.theme.system.StyleConfiguration kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity$ #androidx.activity.ComponentActivity